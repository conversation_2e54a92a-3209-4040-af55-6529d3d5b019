/**
 * Utility functions for Staff Server
 * Common helper functions and utilities
 */

import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { 
  ApiResponse, 
  PaginatedResponse, 
  PaginationParams,
  HttpStatus 
} from '../../../shared/types/common';
import { 
  formatCurrency, 
  formatDate, 
  formatRelativeTime,
  createApiResponse,
  createPaginatedResponse
} from '../../../shared/utils/helpers';

/**
 * Combine class names with Tailwind CSS merge
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Create standardized API response
 */
export function createResponse<T>(
  data?: T,
  success: boolean = true,
  message?: string,
  error?: string,
  status: number = HttpStatus.OK
): Response {
  const response = createApiResponse(data, success, message, error);
  
  return new Response(JSON.stringify(response), {
    status,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * Create error response
 */
export function createErrorResponse(
  error: string,
  status: number = HttpStatus.BAD_REQUEST
): Response {
  return createResponse(undefined, false, undefined, error, status);
}

/**
 * Create success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = HttpStatus.OK
): Response {
  return createResponse(data, true, message, undefined, status);
}

/**
 * Create paginated response
 */
export function createPaginatedApiResponse<T>(
  data: T[],
  pagination: PaginationParams,
  total: number,
  message?: string
): Response {
  const paginatedData = createPaginatedResponse(data, pagination, total);
  return createSuccessResponse(paginatedData, message);
}

/**
 * Parse pagination parameters from URL search params
 */
export function parsePaginationParams(searchParams: URLSearchParams): PaginationParams {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
  const limit = Math.min(100, Math.max(5, parseInt(searchParams.get('limit') || '20')));
  const sortBy = searchParams.get('sortBy') || undefined;
  const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';
  
  return { page, limit, sortBy, sortOrder };
}

/**
 * Parse filter parameters from URL search params
 */
export function parseFilterParams(searchParams: URLSearchParams): Record<string, any> {
  const filters: Record<string, any> = {};
  
  // Common filter parameters
  const search = searchParams.get('search');
  if (search) filters.search = search;
  
  const dateFrom = searchParams.get('dateFrom');
  if (dateFrom) filters.dateFrom = dateFrom;
  
  const dateTo = searchParams.get('dateTo');
  if (dateTo) filters.dateTo = dateTo;
  
  const status = searchParams.get('status');
  if (status) filters.status = status;
  
  // Add other common filters
  for (const [key, value] of searchParams.entries()) {
    if (!['page', 'limit', 'sortBy', 'sortOrder'].includes(key) && !filters[key]) {
      filters[key] = value;
    }
  }
  
  return filters;
}

/**
 * Validate required fields in request body
 */
export function validateRequiredFields(
  body: any,
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    if (body[field] === undefined || body[field] === null || body[field] === '') {
      missingFields.push(field);
    }
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

/**
 * Sanitize input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Generate random ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Sleep function for delays
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Check if string is valid email
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Check if string is valid UUID
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Capitalize first letter of string
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert camelCase to Title Case
 */
export function camelToTitle(str: string): string {
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

/**
 * Truncate string to specified length
 */
export function truncate(str: string, length: number): string {
  if (str.length <= length) return str;
  return str.substring(0, length) + '...';
}

/**
 * Get initials from name
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
}

/**
 * Format phone number
 */
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  return phone;
}

/**
 * Calculate percentage
 */
export function calculatePercentage(value: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((value / total) * 100);
}

/**
 * Get color for status
 */
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    // Student statuses
    active: 'text-green-600 bg-green-100',
    inactive: 'text-gray-600 bg-gray-100',
    graduated: 'text-blue-600 bg-blue-100',
    dropped: 'text-red-600 bg-red-100',
    
    // Lead statuses
    new: 'text-blue-600 bg-blue-100',
    contacted: 'text-yellow-600 bg-yellow-100',
    interested: 'text-green-600 bg-green-100',
    enrolled: 'text-green-600 bg-green-100',
    rejected: 'text-red-600 bg-red-100',
    
    // General statuses
    pending: 'text-yellow-600 bg-yellow-100',
    completed: 'text-green-600 bg-green-100',
    cancelled: 'text-red-600 bg-red-100',
    confirmed: 'text-blue-600 bg-blue-100',
  };
  
  return statusColors[status.toLowerCase()] || 'text-gray-600 bg-gray-100';
}

/**
 * Format student name
 */
export function formatStudentName(firstName: string, lastName: string): string {
  return `${firstName} ${lastName}`.trim();
}

/**
 * Calculate age from date of birth
 */
export function calculateAge(dateOfBirth: Date): number {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

/**
 * Export utility functions from shared helpers
 */
export {
  formatCurrency,
  formatDate,
  formatRelativeTime
};

/**
 * Handle async operations with error catching
 */
export async function handleAsync<T>(
  promise: Promise<T>
): Promise<[T | null, Error | null]> {
  try {
    const data = await promise;
    return [data, null];
  } catch (error) {
    return [null, error as Error];
  }
}
