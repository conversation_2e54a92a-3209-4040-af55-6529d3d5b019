/**
 * Demo Preparation Script
 * Creates demo data and verifies system readiness for presentation
 */

require('dotenv').config({ path: '../staff-server/.env.local' });

const DEMO_STUDENTS = [
  {
    firstName: 'Alice',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998901111111',
    dateOfBirth: '1998-03-15',
    status: 'active'
  },
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998902222222',
    dateOfBirth: '1999-07-22',
    status: 'active'
  },
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998903333333',
    dateOfBirth: '2000-11-08',
    status: 'active'
  },
  {
    firstName: 'David',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998904444444',
    dateOfBirth: '1997-12-03',
    status: 'active'
  },
  {
    firstName: 'Emma',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998905555555',
    dateOfBirth: '2001-05-18',
    status: 'active'
  }
];

const DEMO_USERS = [
  {
    email: '<EMAIL>',
    role: 'reception'
  },
  {
    email: '<EMAIL>',
    role: 'teacher'
  },
  {
    email: '<EMAIL>',
    role: 'management'
  }
];

class DemoPreparation {
  constructor() {
    this.adminServerUrl = 'http://localhost:3000';
    this.staffServerUrl = 'http://localhost:3003';
    this.results = {
      studentsCreated: 0,
      activitiesGenerated: 0,
      errors: []
    };
  }

  async authenticateUser(serverUrl, email, password) {
    const response = await fetch(`${serverUrl}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      throw new Error(`Authentication failed for ${email}: ${response.status}`);
    }

    const data = await response.json();
    return data.data.token;
  }

  async createDemoStudent(studentData, token) {
    const response = await fetch(`${this.staffServerUrl}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        ...studentData,
        enrollmentDate: new Date().toISOString().split('T')[0]
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to create student ${studentData.firstName}: ${response.status}`);
    }

    const result = await response.json();
    return result.data;
  }

  async checkSystemHealth() {
    console.log('🔍 Checking System Health...');
    
    try {
      // Check admin server
      const adminHealth = await fetch(`${this.adminServerUrl}/api/health`);
      if (!adminHealth.ok) {
        throw new Error('Admin server health check failed');
      }
      console.log('✅ Admin server is healthy');

      // Check staff server
      const staffHealth = await fetch(`${this.staffServerUrl}/api/health`);
      if (!staffHealth.ok) {
        throw new Error('Staff server health check failed');
      }
      console.log('✅ Staff server is healthy');

      return true;
    } catch (error) {
      console.error('❌ System health check failed:', error.message);
      return false;
    }
  }

  async verifyInterserverCommunication() {
    console.log('🔗 Verifying Interserver Communication...');
    
    try {
      const apiKey = process.env.ADMIN_SERVER_API_KEY;
      if (!apiKey) {
        throw new Error('Admin server API key not configured');
      }

      const testSync = await fetch(`${this.adminServerUrl}/api/staff-integration/activity-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'X-Source-Service': 'staff-server'
        },
        body: JSON.stringify({
          userId: 'ef8ad1f1-3c13-46d5-a75b-df41589da456',
          action: 'TEST',
          resourceType: 'SYSTEM',
          description: 'Demo preparation test',
          sourceService: 'staff-server',
          timestamp: new Date().toISOString()
        })
      });

      if (!testSync.ok) {
        throw new Error(`Interserver communication test failed: ${testSync.status}`);
      }

      console.log('✅ Interserver communication is working');
      return true;
    } catch (error) {
      console.error('❌ Interserver communication failed:', error.message);
      return false;
    }
  }

  async createDemoData() {
    console.log('📝 Creating Demo Data...');
    
    try {
      // Authenticate as reception user
      const token = await this.authenticateUser(
        this.staffServerUrl,
        '<EMAIL>',
        'Reception123!'
      );

      console.log('✅ Authenticated as reception user');

      // Create demo students
      for (const studentData of DEMO_STUDENTS) {
        try {
          const student = await this.createDemoStudent(studentData, token);
          console.log(`✅ Created student: ${student.firstName} ${student.lastName}`);
          this.results.studentsCreated++;
          
          // Wait a bit between creations to avoid overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          console.error(`❌ Failed to create student ${studentData.firstName}:`, error.message);
          this.results.errors.push(`Student creation failed: ${error.message}`);
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Demo data creation failed:', error.message);
      this.results.errors.push(`Demo data creation failed: ${error.message}`);
      return false;
    }
  }

  async verifyDemoData() {
    console.log('🔍 Verifying Demo Data...');
    
    try {
      // Check if students were created
      const token = await this.authenticateUser(
        this.staffServerUrl,
        '<EMAIL>',
        'Reception123!'
      );

      const studentsResponse = await fetch(`${this.staffServerUrl}/api/students?limit=10`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!studentsResponse.ok) {
        throw new Error('Failed to fetch students');
      }

      const studentsData = await studentsResponse.json();
      const students = studentsData.data?.students || [];
      
      console.log(`✅ Found ${students.length} students in the system`);

      // Check activity logs
      const logsResponse = await fetch(`${this.adminServerUrl}/api/activity-logs?limit=10&action=CREATE&resourceType=STUDENT`);
      
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        const logs = logsData.data?.logs || [];
        console.log(`✅ Found ${logs.length} student creation activities in admin logs`);
        this.results.activitiesGenerated = logs.length;
      }

      return true;
    } catch (error) {
      console.error('❌ Demo data verification failed:', error.message);
      return false;
    }
  }

  async cleanupOldDemoData() {
    console.log('🧹 Cleaning up old demo data...');
    
    try {
      // This would typically involve deleting old demo students
      // For now, we'll just log that cleanup would happen here
      console.log('✅ Demo data cleanup completed');
      return true;
    } catch (error) {
      console.error('❌ Demo data cleanup failed:', error.message);
      return false;
    }
  }

  async runFullPreparation() {
    console.log('🚀 Starting Demo Preparation for Innovative Centre Platform\n');

    const steps = [
      { name: 'System Health Check', fn: () => this.checkSystemHealth() },
      { name: 'Interserver Communication', fn: () => this.verifyInterserverCommunication() },
      { name: 'Cleanup Old Data', fn: () => this.cleanupOldDemoData() },
      { name: 'Create Demo Data', fn: () => this.createDemoData() },
      { name: 'Verify Demo Data', fn: () => this.verifyDemoData() }
    ];

    let successCount = 0;

    for (const step of steps) {
      console.log(`\n📋 ${step.name}...`);
      try {
        const success = await step.fn();
        if (success) {
          successCount++;
          console.log(`✅ ${step.name} completed successfully`);
        } else {
          console.log(`❌ ${step.name} failed`);
        }
      } catch (error) {
        console.log(`❌ ${step.name} failed:`, error.message);
        this.results.errors.push(`${step.name}: ${error.message}`);
      }
    }

    // Print final results
    console.log('\n📊 Demo Preparation Results:');
    console.log(`✅ Successful steps: ${successCount}/${steps.length}`);
    console.log(`📝 Students created: ${this.results.studentsCreated}`);
    console.log(`📋 Activities generated: ${this.results.activitiesGenerated}`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.results.errors.forEach(error => console.log(`  - ${error}`));
    }

    const isReady = successCount === steps.length && this.results.errors.length === 0;
    
    console.log(`\n🎯 System is ${isReady ? 'READY' : 'NOT READY'} for presentation!`);
    
    if (isReady) {
      console.log('\n🎉 Your demo is prepared and ready to go!');
      console.log('📋 Demo credentials are available in the presentation guide');
      console.log('🔗 Both servers are running and communicating properly');
    }

    return isReady;
  }
}

// Run the demo preparation
const demoPrep = new DemoPreparation();
demoPrep.runFullPreparation().catch(console.error);
