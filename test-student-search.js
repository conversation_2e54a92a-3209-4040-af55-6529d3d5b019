/**
 * Test script to verify student search functionality between admin and staff servers
 */

const ADMIN_SERVER_URL = 'http://localhost:3000';
const STAFF_SERVER_URL = 'http://localhost:3003';

async function testStaffServerDirectly() {
  console.log('\n🔍 Testing staff server student API directly...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/students?search=alice&limit=5`, {
      headers: {
        'X-API-Key': 'staff-server-api-key-innovative-centre-2024',
        'X-Source-Service': 'admin-server'
      }
    });
    
    console.log('📡 Staff server response status:', response.status);
    const data = await response.text();
    console.log('📡 Staff server response:', data);
    
    if (response.ok) {
      const jsonData = JSON.parse(data);
      console.log('✅ Staff server students found:', jsonData.data?.students?.length || 0);
      if (jsonData.data?.students?.length > 0) {
        console.log('👤 First student:', jsonData.data.students[0]);
      }
    }
  } catch (error) {
    console.error('❌ Staff server test failed:', error.message);
  }
}

async function testAdminServerStudentSearch() {
  console.log('\n🔍 Testing admin server student search API...');
  
  try {
    // First, we need to authenticate with the admin server
    console.log('🔐 Authenticating with admin server...');
    
    const loginResponse = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!'
      })
    });
    
    console.log('📡 Admin login response status:', loginResponse.status);
    
    if (!loginResponse.ok) {
      console.error('❌ Admin login failed');
      return;
    }
    
    const loginData = await loginResponse.json();
    const authToken = loginData.data?.token;
    
    if (!authToken) {
      console.error('❌ No auth token received');
      return;
    }
    
    console.log('✅ Admin authentication successful');
    
    // Now test the student search
    console.log('🔍 Testing student search...');
    
    const searchResponse = await fetch(`${ADMIN_SERVER_URL}/api/students/search?search=alice&limit=5`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📡 Student search response status:', searchResponse.status);
    const searchData = await searchResponse.text();
    console.log('📡 Student search response:', searchData);
    
    if (searchResponse.ok) {
      const jsonData = JSON.parse(searchData);
      console.log('✅ Students found via admin server:', jsonData.data?.students?.length || 0);
      if (jsonData.data?.students?.length > 0) {
        console.log('👤 First student:', jsonData.data.students[0]);
      }
    }
    
  } catch (error) {
    console.error('❌ Admin server test failed:', error.message);
  }
}

async function main() {
  console.log('🚀 Testing student search functionality...');
  
  await testStaffServerDirectly();
  await testAdminServerStudentSearch();
  
  console.log('\n✅ Test completed!');
}

main().catch(console.error);
