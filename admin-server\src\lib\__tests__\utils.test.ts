/**
 * Utils Library Tests
 * Unit tests for utility functions
 */

import {
  createResponse,
  createErrorResponse,
  parsePaginationParams,
  parseFilterParams,
  validateRequiredFields,
  isValidUUID,
  formatCurrency,
  formatDate,
  generateId,
  sanitizeInput,
  validateEmail,
  validatePassword,
} from '../utils';

describe('Utils Library', () => {
  describe('createResponse', () => {
    it('should create a successful response', () => {
      const data = { test: 'data' };
      const response = createResponse(data, true, 'Success');
      
      expect(response).toEqual({
        data,
        success: true,
        message: 'Success',
      });
    });

    it('should create a response with default values', () => {
      const data = { test: 'data' };
      const response = createResponse(data);
      
      expect(response).toEqual({
        data,
        success: true,
        message: 'Operation completed successfully',
      });
    });

    it('should create a response with custom status', () => {
      const data = { test: 'data' };
      const response = createResponse(data, true, 'Created', undefined, 201);
      
      expect(response.status).toBe(201);
    });
  });

  describe('createErrorResponse', () => {
    it('should create an error response', () => {
      const response = createErrorResponse('Test error', 400);
      
      expect(response).toEqual({
        success: false,
        error: 'Test error',
        status: 400,
      });
    });

    it('should create an error response with default status', () => {
      const response = createErrorResponse('Test error');
      
      expect(response.status).toBe(500);
    });
  });

  describe('parsePaginationParams', () => {
    it('should parse pagination parameters', () => {
      const searchParams = new URLSearchParams({
        page: '2',
        limit: '20',
        sortBy: 'name',
        sortOrder: 'desc',
      });
      
      const result = parsePaginationParams(searchParams);
      
      expect(result).toEqual({
        page: 2,
        limit: 20,
        sortBy: 'name',
        sortOrder: 'desc',
      });
    });

    it('should use default values for missing parameters', () => {
      const searchParams = new URLSearchParams();
      const result = parsePaginationParams(searchParams);
      
      expect(result).toEqual({
        page: 1,
        limit: 10,
        sortBy: undefined,
        sortOrder: 'asc',
      });
    });

    it('should handle invalid values', () => {
      const searchParams = new URLSearchParams({
        page: 'invalid',
        limit: '-5',
        sortOrder: 'invalid',
      });
      
      const result = parsePaginationParams(searchParams);
      
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.sortOrder).toBe('asc');
    });
  });

  describe('parseFilterParams', () => {
    it('should parse filter parameters', () => {
      const searchParams = new URLSearchParams({
        search: 'test query',
        status: 'active',
        dateFrom: '2024-01-01',
        dateTo: '2024-12-31',
      });
      
      const result = parseFilterParams(searchParams);
      
      expect(result).toEqual({
        search: 'test query',
        status: 'active',
        dateFrom: '2024-01-01',
        dateTo: '2024-12-31',
      });
    });

    it('should handle empty parameters', () => {
      const searchParams = new URLSearchParams();
      const result = parseFilterParams(searchParams);
      
      expect(result).toEqual({});
    });
  });

  describe('validateRequiredFields', () => {
    it('should validate required fields successfully', () => {
      const data = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30,
      };
      
      const result = validateRequiredFields(data, ['name', 'email']);
      
      expect(result.isValid).toBe(true);
      expect(result.missingFields).toEqual([]);
    });

    it('should identify missing fields', () => {
      const data = {
        name: 'John Doe',
        age: 30,
      };
      
      const result = validateRequiredFields(data, ['name', 'email', 'phone']);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['email', 'phone']);
    });

    it('should handle empty string values as missing', () => {
      const data = {
        name: '',
        email: '<EMAIL>',
      };
      
      const result = validateRequiredFields(data, ['name', 'email']);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['name']);
    });
  });

  describe('isValidUUID', () => {
    it('should validate correct UUIDs', () => {
      const validUUIDs = [
        '123e4567-e89b-12d3-a456-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
      ];
      
      validUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid)).toBe(true);
      });
    });

    it('should reject invalid UUIDs', () => {
      const invalidUUIDs = [
        'not-a-uuid',
        '123e4567-e89b-12d3-a456',
        '123e4567-e89b-12d3-a456-************-extra',
        '',
        null,
        undefined,
      ];
      
      invalidUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid as any)).toBe(false);
      });
    });
  });

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1234)).toBe('1 234 сўм');
      expect(formatCurrency(0)).toBe('0 сўм');
      expect(formatCurrency(1000000)).toBe('1 000 000 сўм');
    });

    it('should handle negative amounts', () => {
      expect(formatCurrency(-1234)).toBe('-1 234 сўм');
    });

    it('should handle different currencies', () => {
      expect(formatCurrency(1234, 'USD')).toBe('$1,234');
      expect(formatCurrency(1234, 'EUR')).toBe('€1,234');
    });
  });

  describe('formatDate', () => {
    it('should format dates correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatDate(date)).toBe('1/15/2024');
    });

    it('should handle string dates', () => {
      expect(formatDate('2024-01-15')).toBe('1/15/2024');
    });

    it('should handle custom formats', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatDate(date, 'long')).toContain('January');
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });

    it('should generate IDs with specified length', () => {
      const id = generateId(16);
      expect(id.length).toBe(16);
    });
  });

  describe('sanitizeInput', () => {
    it('should sanitize HTML input', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const sanitized = sanitizeInput(input);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Hello World');
    });

    it('should handle null and undefined', () => {
      expect(sanitizeInput(null)).toBe('');
      expect(sanitizeInput(undefined)).toBe('');
    });

    it('should preserve safe content', () => {
      const input = 'Hello World! This is safe content.';
      expect(sanitizeInput(input)).toBe(input);
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      
      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        '<EMAIL>',
        'user@.com',
        '',
      ];
      
      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const strongPasswords = [
        'Password123!',
        'MyStr0ngP@ssw0rd',
        'C0mpl3x!P@ssw0rd',
      ];
      
      strongPasswords.forEach(password => {
        expect(validatePassword(password)).toBe(true);
      });
    });

    it('should reject weak passwords', () => {
      const weakPasswords = [
        'password',
        '12345678',
        'PASSWORD',
        'Pass123',
        'short',
      ];
      
      weakPasswords.forEach(password => {
        expect(validatePassword(password)).toBe(false);
      });
    });
  });
});
