/**
 * Add sample data to staff database for testing
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

// Sample data
const sampleStudents = [
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    enrollment_date: '2024-01-15',
    status: 'active'
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567891',
    enrollment_date: '2024-02-20',
    status: 'active'
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567892',
    enrollment_date: '2024-03-10',
    status: 'inactive'
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567893',
    enrollment_date: '2024-06-01',
    status: 'active'
  },
  {
    first_name: '<PERSON>',
    last_name: 'Brown',
    email: '<EMAIL>',
    phone: '+1234567894',
    enrollment_date: '2024-06-15',
    status: 'active'
  }
];

const sampleLeads = [
  {
    first_name: 'Frank',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567895',
    source: 'website',
    status: 'new'
  },
  {
    first_name: 'Grace',
    last_name: 'Taylor',
    email: '<EMAIL>',
    phone: '+1234567896',
    source: 'referral',
    status: 'contacted'
  },
  {
    first_name: 'Henry',
    last_name: 'Anderson',
    email: '<EMAIL>',
    phone: '+1234567897',
    source: 'social_media',
    status: 'interested'
  },
  {
    first_name: 'Ivy',
    last_name: 'Thomas',
    email: '<EMAIL>',
    phone: '+1234567898',
    source: 'website',
    status: 'enrolled'
  },
  {
    first_name: 'Jack',
    last_name: 'Jackson',
    email: '<EMAIL>',
    phone: '+1234567899',
    source: 'walk_in',
    status: 'rejected'
  }
];

const sampleGroups = [
  {
    name: 'Beginner English A1',
    level: 'A1',
    max_students: 12,
    is_active: true
  },
  {
    name: 'Elementary English A2',
    level: 'A2',
    max_students: 15,
    is_active: true
  },
  {
    name: 'Intermediate English B1',
    level: 'B1',
    max_students: 10,
    is_active: true
  },
  {
    name: 'Upper-Intermediate B2',
    level: 'B2',
    max_students: 8,
    is_active: true
  },
  {
    name: 'Advanced English C1',
    level: 'C1',
    max_students: 6,
    is_active: false
  }
];

async function addSampleData() {
  const pool = new Pool(dbConfig);

  try {
    console.log('🚀 Adding sample data to staff database...');

    // Test connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully:', testResult.rows[0].current_time);

    // Add sample students
    console.log('\n👥 Adding sample students...');
    for (const student of sampleStudents) {
      try {
        // Check if student already exists
        const existingStudent = await pool.query(
          'SELECT id FROM students WHERE email = $1',
          [student.email]
        );

        if (existingStudent.rows.length > 0) {
          console.log(`  ℹ️  Student ${student.first_name} ${student.last_name} already exists`);
          continue;
        }

        const result = await pool.query(
          `INSERT INTO students (first_name, last_name, email, phone, enrollment_date, status)
           VALUES ($1, $2, $3, $4, $5, $6)
           RETURNING id, first_name, last_name`,
          [student.first_name, student.last_name, student.email, student.phone, student.enrollment_date, student.status]
        );

        console.log(`  ✅ Added student: ${result.rows[0].first_name} ${result.rows[0].last_name}`);
      } catch (error) {
        console.error(`  ❌ Error adding student ${student.first_name} ${student.last_name}:`, error.message);
      }
    }

    // Add sample leads
    console.log('\n🎯 Adding sample leads...');
    for (const lead of sampleLeads) {
      try {
        // Check if lead already exists
        const existingLead = await pool.query(
          'SELECT id FROM leads WHERE email = $1',
          [lead.email]
        );

        if (existingLead.rows.length > 0) {
          console.log(`  ℹ️  Lead ${lead.first_name} ${lead.last_name} already exists`);
          continue;
        }

        const result = await pool.query(
          `INSERT INTO leads (first_name, last_name, email, phone, source, status)
           VALUES ($1, $2, $3, $4, $5, $6)
           RETURNING id, first_name, last_name`,
          [lead.first_name, lead.last_name, lead.email, lead.phone, lead.source, lead.status]
        );

        console.log(`  ✅ Added lead: ${result.rows[0].first_name} ${result.rows[0].last_name}`);
      } catch (error) {
        console.error(`  ❌ Error adding lead ${lead.first_name} ${lead.last_name}:`, error.message);
      }
    }

    // Add sample groups
    console.log('\n📚 Adding sample groups...');
    for (const group of sampleGroups) {
      try {
        // Check if group already exists
        const existingGroup = await pool.query(
          'SELECT id FROM groups WHERE name = $1',
          [group.name]
        );

        if (existingGroup.rows.length > 0) {
          console.log(`  ℹ️  Group ${group.name} already exists`);
          continue;
        }

        const result = await pool.query(
          `INSERT INTO groups (name, level, max_students, is_active)
           VALUES ($1, $2, $3, $4)
           RETURNING id, name`,
          [group.name, group.level, group.max_students, group.is_active]
        );

        console.log(`  ✅ Added group: ${result.rows[0].name}`);
      } catch (error) {
        console.error(`  ❌ Error adding group ${group.name}:`, error.message);
      }
    }

    // Add some activity logs
    console.log('\n📋 Adding sample activity logs...');
    try {
      await pool.query(
        `INSERT INTO activity_logs (user_id, action, resource_type, description, timestamp)
         VALUES
         ('00000000-0000-0000-0000-000000000001', 'CREATE', 'student', 'Student enrollment completed', NOW() - INTERVAL '1 day'),
         ('00000000-0000-0000-0000-000000000001', 'UPDATE', 'lead', 'Lead status updated to interested', NOW() - INTERVAL '2 days'),
         ('00000000-0000-0000-0000-000000000001', 'CREATE', 'group', 'New group created', NOW() - INTERVAL '3 days')`
      );
      console.log('  ✅ Added sample activity logs');
    } catch (error) {
      console.log('  ℹ️  Activity logs may already exist or user IDs not found');
    }

    // Verify data
    console.log('\n🔍 Verifying sample data...');
    const studentCount = await pool.query('SELECT COUNT(*) as count FROM students');
    const leadCount = await pool.query('SELECT COUNT(*) as count FROM leads');
    const groupCount = await pool.query('SELECT COUNT(*) as count FROM groups');
    const activityCount = await pool.query('SELECT COUNT(*) as count FROM activity_logs');

    console.log(`  📊 Students: ${studentCount.rows[0].count}`);
    console.log(`  📊 Leads: ${leadCount.rows[0].count}`);
    console.log(`  📊 Groups: ${groupCount.rows[0].count}`);
    console.log(`  📊 Activity logs: ${activityCount.rows[0].count}`);

    console.log('\n🎉 Sample data added successfully!');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

addSampleData();
