/**
 * Payments API endpoint
 * Handles payment management operations (list, create)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logPaymentOperation, getRequestContext } from '@/lib/activity-logger';
import { PaymentType, PaymentMethod, PaymentStatus, ActivityAction } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';
import { staffService } from '@/lib/staff-service';

interface Payment {
  id: string;
  student_id: string;
  student_name?: string;
  amount: number;
  payment_type: PaymentType;
  payment_method: PaymentMethod;
  description?: string;
  status: PaymentStatus;
  processed_by: string;
  start_date: Date;
  end_date: Date;
  debt_amount?: number;
  is_debt_payment?: boolean;
  created_at: Date;
  updated_at: Date;
}

interface PaymentWithUser extends Payment {
  processed_by_name?: string;
  processed_by_email?: string;
}

interface CreatePaymentRequest {
  studentId: string;
  amount: number;
  paymentType: PaymentType;
  paymentMethod: PaymentMethod;
  description?: string;
  startDate: string;
  endDate: string;
  debtAmount?: number;
  isDebtPayment?: boolean;
}

// GET /api/payments - List payments with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    // Build query conditions
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Filter by student ID
    if (filters.studentId) {
      conditions.push(`p.student_id = $${paramIndex}`);
      params.push(filters.studentId);
      paramIndex++;
    }

    // Filter by payment type
    if (filters.paymentType) {
      conditions.push(`p.payment_type = $${paramIndex}`);
      params.push(filters.paymentType);
      paramIndex++;
    }

    // Filter by payment method
    if (filters.paymentMethod) {
      conditions.push(`p.payment_method = $${paramIndex}`);
      params.push(filters.paymentMethod);
      paramIndex++;
    }

    // Filter by status
    if (filters.status) {
      conditions.push(`p.status = $${paramIndex}`);
      params.push(filters.status);
      paramIndex++;
    }

    // Filter by date range (using start_date for filtering)
    if (filters.dateFrom) {
      conditions.push(`p.start_date >= $${paramIndex}`);
      params.push(filters.dateFrom);
      paramIndex++;
    }

    if (filters.dateTo) {
      conditions.push(`p.start_date <= $${paramIndex}`);
      params.push(filters.dateTo);
      paramIndex++;
    }

    // Filter by amount range
    if (filters.amountFrom) {
      conditions.push(`p.amount >= $${paramIndex}`);
      params.push(parseFloat(filters.amountFrom));
      paramIndex++;
    }

    if (filters.amountTo) {
      conditions.push(`p.amount <= $${paramIndex}`);
      params.push(parseFloat(filters.amountTo));
      paramIndex++;
    }

    // Search in description
    if (filters.search) {
      conditions.push(`p.description ILIKE $${paramIndex}`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    let orderBy = 'p.start_date DESC';
    if (pagination.sortBy) {
      const sortColumn = pagination.sortBy === 'amount' ? 'p.amount' :
                        pagination.sortBy === 'paymentDate' ? 'p.start_date' :
                        pagination.sortBy === 'status' ? 'p.status' :
                        'p.start_date';
      orderBy = `${sortColumn} ${pagination.sortOrder}`;
    }

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM payments p
      ${whereClause}
    `;
    
    const countResult = await query(countSql, params);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const dataSql = `
      SELECT
        p.id,
        p.student_id,
        sr.student_name,
        p.amount,
        p.payment_type,
        p.payment_method,
        p.description,
        p.status,
        p.processed_by,
        p.start_date,
        p.end_date,
        p.debt_amount,
        p.is_debt_payment,
        p.created_at,
        p.updated_at,
        u.name as processed_by_name,
        u.email as processed_by_email
      FROM payments p
      LEFT JOIN users u ON p.processed_by = u.id
      LEFT JOIN student_records sr ON p.student_id = sr.staff_student_id
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(pagination.limit, offset);
    const dataResult = await query<PaymentWithUser>(dataSql, params);

    // Format response
    const payments = dataResult.rows.map(payment => ({
      id: payment.id,
      studentId: payment.student_id,
      studentName: payment.student_name,
      amount: payment.amount,
      paymentType: payment.payment_type,
      paymentMethod: payment.payment_method,
      description: payment.description,
      status: payment.status,
      processedBy: payment.processed_by,
      startDate: payment.start_date,
      endDate: payment.end_date,
      debtAmount: payment.debt_amount,
      isDebtPayment: payment.is_debt_payment,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at,
      processedByUser: payment.processed_by_name ? {
        id: payment.processed_by,
        name: payment.processed_by_name,
        email: payment.processed_by_email
      } : undefined
    }));

    return createResponse({
      data: payments,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit),
        hasNext: pagination.page < Math.ceil(total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    }, true, 'Payments retrieved successfully');

  } catch (error) {
    console.error('Error fetching payments:', error);
    return createErrorResponse('Failed to fetch payments', 500);
  }
}

// POST /api/payments - Create a new payment
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body: CreatePaymentRequest = await request.json();

    // Validate required fields
    const validation = validateRequiredFields(body, [
      'studentId', 'amount', 'paymentMethod', 'startDate', 'endDate'
    ]);

    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const {
      studentId,
      amount,
      paymentMethod,
      description,
      startDate,
      endDate,
      debtAmount = 0,
      isDebtPayment = false
    } = body;

    // Validate amount
    if (amount < PAYMENT_CONFIG.MIN_AMOUNT || amount > PAYMENT_CONFIG.MAX_AMOUNT) {
      return createErrorResponse(
        `Amount must be between ${PAYMENT_CONFIG.MIN_AMOUNT} and ${PAYMENT_CONFIG.MAX_AMOUNT} ${PAYMENT_CONFIG.CURRENCY}`,
        400
      );
    }

    // Validate payment method (only cash or card now)
    if (!Object.values(PaymentMethod).includes(paymentMethod)) {
      return createErrorResponse('Invalid payment method', 400);
    }

    // Parse and validate dates
    const processedStartDate = new Date(startDate);
    const processedEndDate = new Date(endDate);

    if (isNaN(processedStartDate.getTime())) {
      return createErrorResponse('Invalid start date format', 400);
    }

    if (isNaN(processedEndDate.getTime())) {
      return createErrorResponse('Invalid end date format', 400);
    }

    if (processedStartDate >= processedEndDate) {
      return createErrorResponse('End date must be after start date', 400);
    }

    // Validate debt amount if provided
    if (isDebtPayment && (debtAmount < 0 || isNaN(debtAmount))) {
      return createErrorResponse('Invalid debt amount', 400);
    }

    // Get or create student record and student name
    let studentName = '';
    try {
      // Try to get student info from staff server
      const staffStudent = await staffService.getStudent(studentId);
      studentName = `${staffStudent.firstName} ${staffStudent.lastName}`;
    } catch (error) {
      console.warn('Could not fetch student from staff server:', error);
      studentName = studentId; // Fallback to student ID
    }

    // Ensure student record exists in admin database
    const studentRecordSql = `
      INSERT INTO student_records (staff_student_id, student_name, total_debt, last_payment_date)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (staff_student_id)
      DO UPDATE SET
        student_name = EXCLUDED.student_name,
        last_payment_date = EXCLUDED.last_payment_date,
        total_debt = CASE
          WHEN $5 THEN GREATEST(0, student_records.total_debt - $6)
          ELSE student_records.total_debt
        END
      RETURNING id
    `;

    await query(studentRecordSql, [
      studentId,
      studentName,
      isDebtPayment ? Math.max(0, debtAmount - amount) : debtAmount,
      processedEndDate,
      isDebtPayment,
      debtAmount
    ]);

    // Create payment record
    const sql = `
      INSERT INTO payments (
        student_id, amount, payment_type, payment_method,
        description, status, processed_by, start_date, end_date,
        debt_amount, is_debt_payment
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id, student_id, amount, payment_type, payment_method,
                description, status, processed_by, start_date, end_date,
                debt_amount, is_debt_payment, created_at, updated_at
    `;

    const params = [
      studentId,
      amount,
      PaymentType.TUITION, // Always tuition now
      paymentMethod,
      description || null,
      PaymentStatus.COMPLETED, // Default status
      authResult.user.id,
      processedStartDate,
      processedEndDate,
      debtAmount || 0,
      isDebtPayment || false
    ];

    const result = await query<Payment>(sql, params);
    const payment = result.rows[0];

    // Notify staff server about the payment
    try {
      await staffService.notifyPaymentMade({
        studentId,
        amount,
        paymentMethod,
        startDate,
        endDate,
        processedBy: authResult.user.name || authResult.user.email,
        debtAmount
      });
    } catch (error) {
      console.warn('Failed to notify staff server:', error);
      // Don't fail the payment creation if notification fails
    }

    // Log the payment creation
    const context = getRequestContext(request.headers);
    await logPaymentOperation(
      ActivityAction.CREATE,
      authResult.user.id,
      {
        id: payment.id,
        studentId: payment.student_id,
        amount: payment.amount,
        paymentType: payment.payment_type,
        paymentMethod: payment.payment_method,
        description: payment.description,
        status: payment.status
      },
      undefined,
      context
    );

    // Format response
    const responsePayment = {
      id: payment.id,
      studentId: payment.student_id,
      studentName,
      amount: payment.amount,
      paymentType: payment.payment_type,
      paymentMethod: payment.payment_method,
      description: payment.description,
      status: payment.status,
      processedBy: payment.processed_by,
      startDate: payment.start_date,
      endDate: payment.end_date,
      debtAmount: payment.debt_amount,
      isDebtPayment: payment.is_debt_payment,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    };

    return createResponse(
      responsePayment,
      true,
      'Payment recorded successfully',
      undefined,
      201
    );

  } catch (error) {
    console.error('Error creating payment:', error);
    return createErrorResponse('Failed to create payment', 500);
  }
}
