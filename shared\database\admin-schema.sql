-- =====================================================
-- INNOVATIVE CENTRE PLATFORM - ADMIN DATABASE SCHEMA
-- =====================================================
-- This schema is for the Admin Server which handles:
-- - User management (admin, cashier, accountant)
-- - Payment processing and financial operations
-- - Cabinet/classroom management
-- - KPI tracking and reporting
-- - Comprehensive activity logging
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- USER MANAGEMENT
-- =====================================================

-- Users table for admin server authentication and authorization
-- Now supports both admin roles (admin, cashier, accountant) and staff roles (management, reception, teacher)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'cashier', 'accountant', 'management', 'reception', 'teacher')),
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    server_type VARCHAR(20) DEFAULT 'admin' CHECK (server_type IN ('admin', 'staff')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for users table
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- =====================================================
-- ACTIVITY LOGGING SYSTEM
-- =====================================================

-- Activity logs table for comprehensive audit trail
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- Create indexes for activity logs
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_timestamp ON activity_logs(timestamp);
CREATE INDEX idx_activity_logs_resource ON activity_logs(resource_type, resource_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);
CREATE INDEX idx_activity_logs_date ON activity_logs(DATE(timestamp));

-- =====================================================
-- PAYMENT AND FINANCIAL MANAGEMENT
-- =====================================================

-- Payments table for recording all financial transactions
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL,
    amount DECIMAL(12,0) NOT NULL CHECK (amount > 0), -- Changed to UZS (no decimals)
    payment_type VARCHAR(50) NOT NULL CHECK (payment_type IN ('tuition')), -- Simplified to tuition only
    payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('cash', 'card')), -- Simplified to cash/card only
    description TEXT,
    status VARCHAR(50) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'refunded', 'cancelled')),
    processed_by UUID REFERENCES users(id),
    start_date DATE NOT NULL, -- Payment period start date
    end_date DATE NOT NULL, -- Payment period end date
    debt_amount DECIMAL(12,0) DEFAULT 0, -- Outstanding debt amount in UZS
    is_debt_payment BOOLEAN DEFAULT FALSE, -- Whether this payment includes debt
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Student records table for tracking students from staff server
CREATE TABLE student_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    staff_student_id UUID NOT NULL UNIQUE, -- ID from staff server
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    enrollment_date DATE,
    status VARCHAR(50) DEFAULT 'active',
    total_debt DECIMAL(12,0) DEFAULT 0, -- Total outstanding debt in UZS
    last_payment_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for payments table
CREATE INDEX idx_payments_student_id ON payments(student_id);
CREATE INDEX idx_payments_processed_by ON payments(processed_by);
CREATE INDEX idx_payments_start_date ON payments(start_date);
CREATE INDEX idx_payments_end_date ON payments(end_date);
CREATE INDEX idx_payments_status ON payments(status);

-- Create indexes for student records table
CREATE INDEX idx_student_records_staff_id ON student_records(staff_student_id);
CREATE INDEX idx_student_records_name ON student_records(first_name, last_name);
CREATE INDEX idx_student_records_phone ON student_records(phone);
CREATE INDEX idx_payments_type ON payments(payment_type);

-- Invoices table for billing management
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    due_date DATE NOT NULL,
    paid_date DATE,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for invoices table
CREATE INDEX idx_invoices_student_id ON invoices(student_id);
CREATE INDEX idx_invoices_due_date ON invoices(due_date);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_created_by ON invoices(created_by);

-- =====================================================
-- CABINET AND RESOURCE MANAGEMENT
-- =====================================================

-- Cabinets table for classroom/room management
CREATE TABLE cabinets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    equipment TEXT[],
    hourly_rate DECIMAL(8,2),
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for cabinets table
CREATE INDEX idx_cabinets_name ON cabinets(name);
CREATE INDEX idx_cabinets_available ON cabinets(is_available);

-- Cabinet bookings table for scheduling
CREATE TABLE cabinet_bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cabinet_id UUID REFERENCES cabinets(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    booked_by UUID REFERENCES users(id),
    purpose VARCHAR(255),
    status VARCHAR(50) DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'pending', 'cancelled', 'completed')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure end time is after start time
    CONSTRAINT check_booking_time CHECK (end_time > start_time)
);

-- Create indexes for cabinet bookings
CREATE INDEX idx_cabinet_bookings_cabinet_id ON cabinet_bookings(cabinet_id);
CREATE INDEX idx_cabinet_bookings_date ON cabinet_bookings(date);
CREATE INDEX idx_cabinet_bookings_booked_by ON cabinet_bookings(booked_by);
CREATE INDEX idx_cabinet_bookings_status ON cabinet_bookings(status);

-- Unique constraint to prevent double booking
CREATE UNIQUE INDEX idx_cabinet_bookings_unique ON cabinet_bookings(cabinet_id, date, start_time, end_time) 
WHERE status IN ('confirmed', 'pending');

-- =====================================================
-- KPI TRACKING
-- =====================================================

-- Teacher KPIs table for performance tracking
CREATE TABLE teacher_kpis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    teacher_id UUID NOT NULL,
    month DATE NOT NULL,
    students_taught INTEGER DEFAULT 0 CHECK (students_taught >= 0),
    retention_rate DECIMAL(5,2) DEFAULT 0 CHECK (retention_rate >= 0 AND retention_rate <= 100),
    performance_score DECIMAL(5,2) DEFAULT 0 CHECK (performance_score >= 0 AND performance_score <= 100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for teacher per month
    UNIQUE(teacher_id, month)
);

-- Create indexes for teacher KPIs
CREATE INDEX idx_teacher_kpis_teacher_id ON teacher_kpis(teacher_id);
CREATE INDEX idx_teacher_kpis_month ON teacher_kpis(month);

-- Reception KPIs table for front desk performance
CREATE TABLE reception_kpis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    staff_id UUID NOT NULL,
    month DATE NOT NULL,
    leads_converted INTEGER DEFAULT 0 CHECK (leads_converted >= 0),
    students_enrolled INTEGER DEFAULT 0 CHECK (students_enrolled >= 0),
    satisfaction_score DECIMAL(5,2) DEFAULT 0 CHECK (satisfaction_score >= 0 AND satisfaction_score <= 10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for staff per month
    UNIQUE(staff_id, month)
);

-- Create indexes for reception KPIs
CREATE INDEX idx_reception_kpis_staff_id ON reception_kpis(staff_id);
CREATE INDEX idx_reception_kpis_month ON reception_kpis(month);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cabinets_updated_at BEFORE UPDATE ON cabinets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cabinet_bookings_updated_at BEFORE UPDATE ON cabinet_bookings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teacher_kpis_updated_at BEFORE UPDATE ON teacher_kpis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reception_kpis_updated_at BEFORE UPDATE ON reception_kpis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert default admin user (password: Admin123!)
-- Note: In production, this should be changed immediately
INSERT INTO users (email, password_hash, role, name) VALUES 
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO', 'admin', 'System Administrator');

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- View for payment summary by month
CREATE VIEW monthly_payment_summary AS
SELECT
    DATE_TRUNC('month', start_date) as month,
    payment_type,
    payment_method,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount
FROM payments
WHERE status = 'completed'
GROUP BY DATE_TRUNC('month', start_date), payment_type, payment_method
ORDER BY month DESC;

-- View for cabinet utilization
CREATE VIEW cabinet_utilization AS
SELECT 
    c.id,
    c.name,
    c.capacity,
    COUNT(cb.id) as total_bookings,
    COUNT(CASE WHEN cb.status = 'confirmed' THEN 1 END) as confirmed_bookings,
    ROUND(
        (COUNT(CASE WHEN cb.status = 'confirmed' THEN 1 END)::DECIMAL / 
         NULLIF(COUNT(cb.id), 0)) * 100, 2
    ) as utilization_rate
FROM cabinets c
LEFT JOIN cabinet_bookings cb ON c.id = cb.cabinet_id
GROUP BY c.id, c.name, c.capacity
ORDER BY utilization_rate DESC;
