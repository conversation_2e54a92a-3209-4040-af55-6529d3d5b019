/**
 * Staff Server Integration Service
 * Handles communication with the staff server for student data
 */

interface StaffServerConfig {
  baseUrl: string;
  apiKey: string;
}

interface StaffServerStudent {
  id: string;
  firstName: string;
  lastName: string;
  phone?: string;
  status: string;
  enrollmentDate?: string;
}

interface StaffServerResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface StudentsListResponse {
  students: StaffServerStudent[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export class StaffService {
  private config: StaffServerConfig;

  constructor() {
    this.config = {
      baseUrl: process.env.STAFF_SERVER_URL || '',
      apiKey: process.env.STAFF_SERVER_API_KEY || ''
    };
  }

  /**
   * Check if staff server integration is enabled
   */
  isEnabled(): boolean {
    return !!(this.config.baseUrl && this.config.apiKey);
  }

  /**
   * Make a request to the staff server
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    if (!this.isEnabled()) {
      throw new Error('Staff server integration not configured');
    }

    const url = `${this.config.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'X-API-Key': this.config.apiKey,
        'X-Source-Service': 'admin-server',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Staff server request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'Staff server request failed');
    }

    return data.data;
  }

  /**
   * Search students from staff server
   */
  async searchStudents(params: {
    search?: string;
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<StudentsListResponse> {
    const queryParams = new URLSearchParams();
    
    if (params.search) queryParams.append('search', params.search);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.status) queryParams.append('status', params.status);

    const endpoint = `/api/students${queryParams.toString() ? `?${queryParams}` : ''}`;
    
    return this.makeRequest<StudentsListResponse>(endpoint);
  }

  /**
   * Get specific student from staff server
   */
  async getStudent(studentId: string): Promise<StaffServerStudent> {
    return this.makeRequest<StaffServerStudent>(`/api/students/${studentId}`);
  }

  /**
   * Notify staff server about payment made for a student
   */
  async notifyPaymentMade(paymentData: {
    studentId: string;
    amount: number;
    paymentMethod: string;
    startDate: string;
    endDate: string;
    processedBy: string;
    debtAmount?: number;
  }): Promise<void> {
    try {
      await this.makeRequest('/api/admin-integration/payment-notification', {
        method: 'POST',
        body: JSON.stringify(paymentData),
      });
    } catch (error) {
      console.error('Failed to notify staff server of payment:', error);
      // Don't throw error to avoid breaking payment process
    }
  }

  /**
   * Sync student record to admin server
   */
  async syncStudentRecord(studentData: {
    id: string;
    firstName: string;
    lastName: string;
    phone?: string;
    enrollmentDate?: string;
    status: string;
  }): Promise<void> {
    // This would be called when we need to ensure student record exists in admin server
    // Implementation depends on how we want to handle student record synchronization
  }

  /**
   * Check staff server status
   */
  async checkStatus(): Promise<{
    available: boolean;
    version?: string;
    lastChecked: Date;
  }> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/health`, {
        headers: {
          'X-API-Key': this.config.apiKey,
          'X-Source-Service': 'admin-server',
        },
        timeout: 5000,
      } as RequestInit);

      return {
        available: response.ok,
        version: response.headers.get('X-Version') || undefined,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        available: false,
        lastChecked: new Date(),
      };
    }
  }
}

// Export singleton instance
export const staffService = new StaffService();

// Helper function to check if staff integration is enabled
export function isStaffIntegrationEnabled(): boolean {
  return staffService.isEnabled();
}

// Helper function for safe staff service calls
export async function safeStaffServiceCall<T>(
  operation: () => Promise<T>,
  fallback: T
): Promise<T> {
  try {
    if (!isStaffIntegrationEnabled()) {
      return fallback;
    }
    return await operation();
  } catch (error) {
    console.error('Staff service call failed:', error);
    return fallback;
  }
}
